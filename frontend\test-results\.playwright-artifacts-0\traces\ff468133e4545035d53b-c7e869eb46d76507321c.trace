{"version":8,"type":"context-options","origin":"library","browserName":"chromium","options":{"noDefaultViewport":false,"viewport":{"width":1280,"height":720},"ignoreHTTPSErrors":false,"javaScriptEnabled":true,"bypassCSP":false,"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36","locale":"en-US","offline":false,"deviceScaleFactor":1,"isMobile":false,"hasTouch":false,"colorScheme":"light","acceptDownloads":"accept","baseURL":"http://127.0.0.1:5173","serviceWorkers":"allow","selectorEngines":[],"testIdAttributeName":"data-testid"},"platform":"win32","wallTime":1755761939717,"monotonicTime":65426.79,"sdkLanguage":"javascript","testIdAttributeName":"data-testid","contextId":"browser-context@a66465725c1067c5e8e684eb31208e07","title":"commenter_card_history.spec.ts:7 › should show commenter credibility card when viewing a history session using raw_data.llm_analysis"}
{"type":"before","callId":"call@6","startTime":65439.834,"class":"BrowserContext","method":"newPage","params":{},"stepId":"pw:api@7","beforeSnapshot":"before@call@6"}
