{"version":8,"type":"context-options","origin":"library","browserName":"webkit","options":{"noDefaultViewport":false,"viewport":{"width":390,"height":664},"ignoreHTTPSErrors":false,"javaScriptEnabled":true,"bypassCSP":false,"userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 14_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/26.0 Mobile/15E148 Safari/604.1","locale":"en-US","offline":false,"deviceScaleFactor":3,"isMobile":true,"hasTouch":true,"colorScheme":"light","acceptDownloads":"accept","baseURL":"http://127.0.0.1:5173","serviceWorkers":"allow","selectorEngines":[],"testIdAttributeName":"data-testid"},"platform":"win32","wallTime":1755761981043,"monotonicTime":106744.244,"sdkLanguage":"javascript","testIdAttributeName":"data-testid","contextId":"browser-context@010281c72e318a335cb22511e4714f8d","title":"commenter_card_history.spec.ts:7 › should show commenter credibility card when viewing a history session using raw_data.llm_analysis"}
{"type":"before","callId":"call@6","startTime":106754.977,"class":"BrowserContext","method":"newPage","params":{},"stepId":"pw:api@7","beforeSnapshot":"before@call@6"}
