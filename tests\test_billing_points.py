#!/usr/bin/env python3
"""
Test: Points system functionality
"""

import pytest
import json
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock
from models.database_models import UserAccount, PointsLedger
from config import config as cfg


@pytest.fixture
def mock_auth(monkeypatch):
    """Mock authentication for tests"""
    def mock_verify_token(token):
        # Extract user ID from token if it's in format "test_user_<id>"
        if token.startswith("test_user_"):
            return int(token.split("_")[-1])
        return 1  # Default user ID
    
    # Patch all possible import locations
    monkeypatch.setattr("api.tokens.verify_token", mock_verify_token)
    monkeypatch.setattr("api.blueprints_search._verify_token", mock_verify_token)
    monkeypatch.setattr("api.blueprints_history._verify_token", mock_verify_token)
    monkeypatch.setattr("api.blueprints_auth._verify_token", mock_verify_token)
    
    return mock_verify_token


class TestPointsSystem:
    """Points system tests"""
    
    def test_user_registration_grants_initial_points(self, test_client, db_session):
        """Test: User receives initial 20 points upon registration"""
        # Ensure DEBUG_MODE is set for this test
        import os
        os.environ['DEBUG_MODE'] = 'True'
        
        # Clear auth fallback to ensure clean state
        from api.blueprints_auth import _auth_fallback
        _auth_fallback.users = {}
        _auth_fallback.pending_verifications = {}
        
        # Use a unique email for this test
        import time
        test_email = f'newuser_{int(time.time())}@test.com'
        
        # Clean up any existing user with this email in database
        existing_user = db_session.query(UserAccount).filter_by(email=test_email).first()
        if existing_user:
            db_session.delete(existing_user)
            db_session.commit()
        
        # Register new user
        response = test_client.post('/api/auth/register', json={
            'email': test_email,
            'password': 'testpass123'
        })
        
        assert response.status_code == 200
        data = response.get_json()
        print(f"Registration response: {data}")
        assert data['success'] is True
        assert data.get('need_verification') is True
        
        # In debug mode, we should get the verification code
        verification_code = data.get('dev_code')
        if not verification_code:
            # Read verification code from local JSON storage
            from pathlib import Path
            import json
            codes_file = Path(cfg.DATA_DIR) / "auth_email_codes.json"
            if codes_file.exists():
                codes = json.loads(codes_file.read_text(encoding="utf-8"))
                entry = (codes.get("codes") or {}).get(test_email.lower())
                if entry and entry.get("code"):
                    verification_code = str(entry.get("code"))
                    print(f"Got verification code from JSON file: {verification_code}")
            
        if not verification_code:
            pytest.skip(f"No verification code available (DEBUG_MODE={os.environ.get('DEBUG_MODE')})")
        
        print(f"Using verification code: {verification_code}")
        
        # Now verify the email to complete registration
        verify_response = test_client.post('/api/auth/verify', json={
            'email': test_email,
            'code': verification_code
        })
        
        assert verify_response.status_code == 200
        verify_data = verify_response.get_json()
        print(f"Verification response: {verify_data}")
        assert verify_data['success'] is True
        
        # Refresh the session to get updated data
        db_session.expire_all()
        
        # Check user in database
        user = db_session.query(UserAccount).filter_by(email=test_email).first()
        assert user is not None
        print(f"User found: id={user.id}, points_balance={user.points_balance}, trial_flags={user.trial_flags}")
        
        # After registration and email verification, user should have 40 points total
        # (20 for registration + 20 for email verification)
        assert user.points_balance == cfg.TRIAL_INITIAL_POINTS + cfg.TRIAL_VERIFIED_POINTS  # 40 points
        assert user.trial_flags.get('granted20') is True
        assert user.trial_flags.get('granted40') is True
        
        # Check ledger entry
        ledger = db_session.query(PointsLedger).filter_by(
            user_id=user.id,
            reason='trial'
        ).first()
        assert ledger is not None
        assert ledger.delta == cfg.TRIAL_INITIAL_POINTS + cfg.TRIAL_VERIFIED_POINTS  # 40 points total
        assert ledger.ref == 'registration_bonus'
    
    def test_email_verification_no_additional_points(self, test_client, db_session):
        """Test: Email verification does not grant additional points if already granted"""
        # First create user who already has all points
        user = UserAccount(
            email='<EMAIL>',
            password_hash='dummy_hash',
            points_balance=40,  # Already has all points
            trial_flags={'granted20': True, 'granted40': True}
        )
        db_session.add(user)
        db_session.commit()
        
        # Mock verification code
        with patch('services.auth_service.AuthService.verify_code', return_value=True):
            response = test_client.post('/api/auth/verify', json={
                'email': '<EMAIL>',
                'code': '123456'
            })
        
        assert response.status_code == 200
        
        # Refresh user data
        db_session.refresh(user)
        assert user.points_balance == 40  # No change
        assert user.trial_flags.get('granted40') is True
        
        # Check that no new ledger entry was created
        ledger_count = db_session.query(PointsLedger).filter_by(
            user_id=user.id,
            reason='trial'
        ).count()
        assert ledger_count == 0  # No new entries
    
    def test_search_deducts_points(self, test_client, db_session, auth_headers):
        """Test: Successful search deducts 20 points"""
        # Create user with balance
        from models.database_models import UserAccount as UA
        user = UA(
            email='<EMAIL>',
            password_hash='dummy_hash',
            points_balance=100,
            email_verified=True
        )
        db_session.add(user)
        db_session.commit()
        
        # Mock login
        with patch('api.blueprints_search._verify_token', return_value=user.id):
            # Initiate search
            response = test_client.post('/api/search', 
                headers=auth_headers,
                json={'query': 'test search'}
            )
            
            assert response.status_code == 200
            data = response.get_json()
            session_id = data['session_id']
            
            # Simulate search completion (in actual implementation this happens in background thread)
            # Here we directly call the deduction logic
            from models.database_models import PointsLedger
            from config import config as cfg
            with db_session.begin():
                user = db_session.get(UA, user.id)
                user.points_balance -= cfg.SEARCH_COST_POINTS
                
                ledger_entry = PointsLedger(
                    user_id=user.id,
                    delta=-cfg.SEARCH_COST_POINTS,
                    reason='search',
                    ref=session_id
                )
                db_session.add(ledger_entry)
        
        # Verify deduction
        db_session.refresh(user)
        assert user.points_balance == 80  # 100 - 20
        
        # Check ledger
        ledger = db_session.query(PointsLedger).filter_by(
            user_id=user.id,
            reason='search'
        ).first()
        assert ledger is not None
        assert ledger.delta == -20
    
    def test_search_fails_when_insufficient_points(self, test_client, db_session, mock_auth):
        """Test: Search fails when insufficient points"""
        # Create user with insufficient points
        user = UserAccount(
            email='<EMAIL>',
            password_hash='dummy_hash',
            points_balance=10,  # Less than 20 points
            email_verified=True,
            trial_flags={'trial_last_used_at': datetime.now().isoformat()}  # Trial already used today
        )
        db_session.add(user)
        db_session.commit()
        
        auth_headers = {'Authorization': f'Bearer test_user_{user.id}'}
        response = test_client.post('/api/search',
            headers=auth_headers,
                json={'query': 'test search'}
            )
        
        assert response.status_code == 402  # Payment Required
        data = response.get_json()
        assert data['success'] is False
        assert 'Insufficient points' in data['error']
        assert data['balance'] == 10
        assert data['required'] == 20
    
    def test_daily_trial_search(self, test_client, db_session, auth_headers):
        """Test: Daily free trial search"""
        # Create user who used trial yesterday
        yesterday = datetime.now() - timedelta(days=1)
        user = UserAccount(
            email='<EMAIL>',
            password_hash='dummy_hash',
            points_balance=0,  # No points
            email_verified=True,
            trial_flags={'trial_last_used_at': yesterday.isoformat()}
        )
        db_session.add(user)
        db_session.commit()
        
        auth_headers = {'Authorization': f'Bearer test_user_{user.id}'}
        response = test_client.post('/api/search',
            headers=auth_headers,
                json={'query': 'test search'}
            )
        
        assert response.status_code == 200  # Should allow search
        
        # Verify trial record will be updated (actually happens after search completes)
        # Here we simulate the update
        user.trial_flags['trial_last_used_at'] = datetime.now().isoformat()
        db_session.commit()
        
        # Same day search should fail
        auth_headers = {'Authorization': f'Bearer test_user_{user.id}'}
        response = test_client.post('/api/search',
            headers=auth_headers,
                json={'query': 'another search'}
            )
        
        assert response.status_code in (402, 403)  # Insufficient points or forbidden





if __name__ == '__main__':
    pytest.main([__file__, '-v'])